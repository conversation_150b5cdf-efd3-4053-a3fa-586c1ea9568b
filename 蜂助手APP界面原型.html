<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蜂助手APP - 吃喝玩乐原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            max-width: 375px;
            margin: 0 auto;
            position: relative;
        }
        
        .phone-frame {
            background: #000;
            padding: 20px 8px;
            border-radius: 25px;
            margin: 20px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .screen {
            background: #fff;
            border-radius: 20px;
            overflow: hidden;
            height: 667px;
            position: relative;
        }
        
        .status-bar {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            padding: 15px;
            color: #333;
        }
        
        .search-box {
            background: rgba(255,255,255,0.9);
            border-radius: 20px;
            padding: 10px 15px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-box input {
            border: none;
            outline: none;
            flex: 1;
            background: transparent;
            font-size: 14px;
        }
        
        .nav-tabs {
            display: flex;
            background: #fff;
            border-bottom: 1px solid #eee;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            cursor: pointer;
        }
        
        .nav-tab.active {
            color: #FFA500;
            border-bottom-color: #FFA500;
            font-weight: 600;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            padding-bottom: 80px; /* 为底部导航留出空间 */
        }
        
        .banner {
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            border-radius: 12px;
            padding: 20px;
            color: white;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .banner h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .banner p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .action-item {
            text-align: center;
            padding: 12px 8px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .action-item:hover {
            transform: translateY(-2px);
        }
        
        .action-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            margin: 0 auto 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .action-text {
            font-size: 11px;
            color: #333;
            font-weight: 500;
            line-height: 1.2;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #FFA500;
            border-radius: 2px;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .product-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
        }
        
        .product-image {
            height: 120px;
            background: linear-gradient(135deg, #FF9A9E, #FECFEF);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: #fff;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
            line-height: 1.3;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .current-price {
            color: #FF4757;
            font-size: 16px;
            font-weight: 600;
        }
        
        .original-price {
            color: #999;
            font-size: 12px;
            text-decoration: line-through;
        }
        
        .discount-tag {
            background: #FF4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: auto;
        }
        
        .activity-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            padding: 20px;
            color: white;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .activity-card::before {
            content: '';
            position: absolute;
            top: -30px;
            right: -30px;
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .activity-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .activity-desc {
            font-size: 13px;
            opacity: 0.9;
            margin-bottom: 12px;
        }
        
        .activity-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            display: inline-block;
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #eee;
            display: flex;
            height: 60px;
        }
        
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            cursor: pointer;
        }
        
        .nav-item.active {
            color: #FFA500;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .floating-btn {
            position: fixed;
            bottom: 140px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(255,107,107,0.4);
            cursor: pointer;
            animation: pulse 2s infinite;
            z-index: 1000;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .page {
            display: none;
            height: calc(100% - 104px);
            overflow-y: auto;
            position: relative;
        }
        
        .page.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>蜂助手</span>
                <span>🔋100%</span>
            </div>
            
            <div class="header">
                <h2>🐝 蜂助手 - 吃喝玩乐</h2>
                <div class="search-box">
                    <span>🔍</span>
                    <input type="text" placeholder="搜索美食、饮品、优惠券...">
                </div>
            </div>
            

            
            <!-- 生活权益 -->
            <div id="rights" class="page">
                <div class="content">
                    <div class="banner">
                        <h3>🎫 生活权益</h3>
                        <p>爱奇艺、优酷、腾讯会员等虚拟充值服务</p>
                    </div>

                    <div class="quick-actions">
                        <div class="action-item">
                            <div class="action-icon">📺</div>
                            <div class="action-text">爱奇艺</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🎬</div>
                            <div class="action-text">优酷</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🎭</div>
                            <div class="action-text">腾讯视频</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🎵</div>
                            <div class="action-text">音乐会员</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 电商返利 -->
            <div id="cashback" class="page">
                <div class="content">
                    <div class="banner">
                        <h3>💰 电商返利</h3>
                        <p>淘宝、京东、拼多多购物返利</p>
                    </div>

                    <div class="quick-actions">
                        <div class="action-item">
                            <div class="action-icon">🛒</div>
                            <div class="action-text">淘宝</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">📦</div>
                            <div class="action-text">京东</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🛍️</div>
                            <div class="action-text">拼多多</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">💳</div>
                            <div class="action-text">返利记录</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大会员 -->
            <div id="vip" class="page">
                <div class="content">
                    <div class="banner">
                        <h3>👑 大会员</h3>
                        <p>开通会员享受更多专属权益</p>
                    </div>

                    <div class="activity-card">
                        <div class="activity-title">🏆 会员特权</div>
                        <div class="activity-desc">全场额外9.5折 + 专属客服 + 生日特权</div>
                        <span class="activity-btn">立即开通</span>
                    </div>
                </div>
            </div>

            <!-- 吃喝玩乐 -->
            <div id="food-fun" class="page active">
                <div class="content">
                    <!-- 顶部轮播横幅 -->
                    <div class="banner">
                        <h3>🎉 蜂狂吃喝节</h3>
                        <p>全场8.8折起 + 新用户50元券包</p>
                    </div>

                    <!-- 快捷入口 -->
                    <div class="section-title">🚀 热门品牌</div>
                    <div class="quick-actions">
                        <div class="action-item">
                            <div class="action-icon">🍔</div>
                            <div class="action-text">肯德基</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🍟</div>
                            <div class="action-text">麦当劳</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">☕</div>
                            <div class="action-text">星巴克</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🥤</div>
                            <div class="action-text">喜茶</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🍕</div>
                            <div class="action-text">必胜客</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🍦</div>
                            <div class="action-text">哈根达斯</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🥪</div>
                            <div class="action-text">赛百味</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🍜</div>
                            <div class="action-text">外卖</div>
                        </div>
                    </div>

                    <!-- 限时秒杀 -->
                    <div class="section-title">⚡ 限时秒杀 <span style="color: #FF4757; font-size: 12px;">距结束 02:15:30</span></div>
                    <div class="product-grid">
                        <div class="product-card">
                            <div class="product-image">🍗</div>
                            <div class="product-info">
                                <div class="product-name">肯德基全家桶</div>
                                <div class="product-price">
                                    <span class="current-price">¥39.9</span>
                                    <span class="original-price">¥89</span>
                                    <span class="discount-tag">5.5折</span>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">☕</div>
                            <div class="product-info">
                                <div class="product-name">星巴克拿铁</div>
                                <div class="product-price">
                                    <span class="current-price">¥19.9</span>
                                    <span class="original-price">¥32</span>
                                    <span class="discount-tag">6.2折</span>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">🍕</div>
                            <div class="product-info">
                                <div class="product-name">必胜客披萨</div>
                                <div class="product-price">
                                    <span class="current-price">¥49.9</span>
                                    <span class="original-price">¥89</span>
                                    <span class="discount-tag">5.6折</span>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">🥤</div>
                            <div class="product-info">
                                <div class="product-name">喜茶券包</div>
                                <div class="product-price">
                                    <span class="current-price">¥29.9</span>
                                    <span class="original-price">¥60</span>
                                    <span class="discount-tag">5折</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 今日特惠 -->
                    <div class="section-title">🎁 今日特惠</div>
                    <div class="activity-card">
                        <div class="activity-title">🎯 薅羊毛大作战</div>
                        <div class="activity-desc">每周末专场活动，1-5折商品限时抢购</div>
                        <span class="activity-btn">立即参与</span>
                    </div>

                    <div class="activity-card" style="background: linear-gradient(135deg, #FF6B6B, #FF8E53);">
                        <div class="activity-title">👥 3人拼团</div>
                        <div class="activity-desc">邀请好友拼团，享受团购价，最低5折起</div>
                        <span class="activity-btn">发起拼团</span>
                    </div>

                    <!-- 外卖专区 -->
                    <div class="section-title">🚚 外卖专区</div>
                    <div class="product-grid">
                        <div class="product-card">
                            <div class="product-image">🍜</div>
                            <div class="product-info">
                                <div class="product-name">兰州拉面</div>
                                <div class="product-price">
                                    <span class="current-price">¥15.8</span>
                                    <span class="original-price">¥22</span>
                                    <span class="discount-tag">返现5%</span>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">🍛</div>
                            <div class="product-info">
                                <div class="product-name">黄焖鸡米饭</div>
                                <div class="product-price">
                                    <span class="current-price">¥18.8</span>
                                    <span class="original-price">¥25</span>
                                    <span class="discount-tag">7.5折</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 新人专享 -->
                    <div class="section-title">🆕 新人专享</div>
                    <div class="activity-card" style="background: linear-gradient(135deg, #4ECDC4, #44A08D);">
                        <div class="activity-title">🎁 新人大礼包</div>
                        <div class="activity-desc">注册即送50元券包，首单立减15元，连续签到送肯德基券</div>
                        <span class="activity-btn">立即领取</span>
                    </div>

                    <!-- 会员专区 -->
                    <div class="section-title">👑 会员专区</div>
                    <div class="activity-card" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <div class="activity-title">🏆 超级会员</div>
                        <div class="activity-desc">开通会员享全场9.5折，每月10张专属券，生日双倍积分</div>
                        <span class="activity-btn">立即开通</span>
                    </div>
                </div>
            </div>

            <!-- 我的 -->
            <div id="profile" class="page">
                <div class="content">
                    <div class="banner">
                        <h3>👤 个人中心</h3>
                        <p>管理您的账户信息和订单</p>
                    </div>

                    <div class="quick-actions">
                        <div class="action-item">
                            <div class="action-icon">📋</div>
                            <div class="action-text">我的订单</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">🎫</div>
                            <div class="action-text">优惠券</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">⭐</div>
                            <div class="action-text">积分</div>
                        </div>
                        <div class="action-item">
                            <div class="action-icon">⚙️</div>
                            <div class="action-text">设置</div>
                        </div>
                    </div>
                </div>
            </div>
            

            
            <div class="floating-btn">💬</div>
            
            <div class="bottom-nav">
                <div class="nav-item" onclick="showPage('rights')">
                    <div class="nav-icon">🎫</div>
                    <div>生活权益</div>
                </div>
                <div class="nav-item" onclick="showPage('cashback')">
                    <div class="nav-icon">💰</div>
                    <div>电商返利</div>
                </div>
                <div class="nav-item" onclick="showPage('vip')">
                    <div class="nav-icon">👑</div>
                    <div>大会员</div>
                </div>
                <div class="nav-item active" onclick="showPage('food-fun')">
                    <div class="nav-icon">🍔</div>
                    <div>吃喝玩乐</div>
                </div>
                <div class="nav-item" onclick="showPage('profile')">
                    <div class="nav-icon">👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示选中页面
            document.getElementById(pageId).classList.add('active');

            // 更新导航状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
